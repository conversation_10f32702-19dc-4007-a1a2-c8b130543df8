'use client'

import type { ForwardedRef } from 'react'
import {
  headingsPlugin,
  listsPlugin,
  quotePlugin,
  thematicBreakPlugin,
  markdownShortcutPlugin,
  linkPlugin,
  linkDialogPlugin,
  imagePlugin,
  tablePlugin,
  codeBlockPlugin,
  codeMirrorPlugin,
  diffSourcePlugin,
  frontmatterPlugin,
  MDXEditor,
  type MDXEditorMethods,
  type MDXEditorProps,
  toolbarPlugin,
  UndoRedo,
  BoldItalicUnderlineToggles,
  CodeToggle,
  CreateLink,
  InsertImage,
  InsertTable,
  InsertThematicBreak,
  ListsToggle,
  BlockTypeSelect,
  Separator,
  DiffSourceToggleWrapper,
} from '@mdxeditor/editor'
import '@mdxeditor/editor/style.css'

// Only import this to the next file
export default function InitializedMDXEditor({
  editorRef,
  ...props
}: { editorRef: ForwardedRef<MDXEditorMethods> | null } & MDXEditorProps) {
  return (
    <MDXEditor
      plugins={[
        // Core plugins for basic markdown functionality
        headingsPlugin(),
        listsPlugin(),
        quotePlugin(),
        thematicBreakPlugin(),
        markdownShortcutPlugin(),
        
        // Link functionality
        linkPlugin(),
        linkDialogPlugin(),
        
        // Image functionality
        imagePlugin(),
        
        // Table functionality
        tablePlugin(),
        
        // Code block functionality
        codeBlockPlugin({ defaultCodeBlockLanguage: 'javascript' }),
        codeMirrorPlugin({ codeBlockLanguages: { 
          js: 'JavaScript', 
          css: 'CSS', 
          txt: 'text', 
          tsx: 'TypeScript',
          ts: 'TypeScript',
          html: 'HTML',
          json: 'JSON',
          python: 'Python',
          bash: 'Bash',
          sql: 'SQL'
        }}),
        
        // Source mode toggle
        diffSourcePlugin({ viewMode: 'rich-text', diffMarkdown: '' }),
        
        // Front matter support
        frontmatterPlugin(),
        
        // Toolbar with comprehensive options
        toolbarPlugin({
          toolbarContents: () => (
            <>
              <UndoRedo />
              <Separator />
              <BoldItalicUnderlineToggles />
              <CodeToggle />
              <Separator />
              <BlockTypeSelect />
              <Separator />
              <ListsToggle />
              <Separator />
              <CreateLink />
              <InsertImage />
              <Separator />
              <InsertTable />
              <InsertThematicBreak />
              <Separator />
              <DiffSourceToggleWrapper>
                <div style={{ padding: '8px' }}>Source</div>
              </DiffSourceToggleWrapper>
            </>
          )
        })
      ]}
      {...props}
      ref={editorRef}
      contentEditableClassName="prose max-w-none"
    />
  )
}
